using NotifyMaster.Core.Interfaces;
using NotifyMaster.Core.Models;
using static NotifyMaster.Core.Interfaces.Messages;
using NotifyMasterApi.Gateways;
using NotificationContract.Models;

namespace NotifyMasterApi.Features.Email;

/// <summary>
/// Handler for processing email notification messages from the queue
/// Implements the queue-first pattern: Queue → Database → Send → Response
/// </summary>
public class SendNotificationMessageHandler : MessageHandlerBase<SendNotificationMessage>
{
    private readonly IEmailGateway _emailGateway;
    private readonly ITenantService _tenantService;
    private readonly IUserService _userService;
    private readonly ITenantContext _tenantContext;

    public SendNotificationMessageHandler(
        ILogger<SendNotificationMessageHandler> logger,
        IEmailGateway emailGateway,
        ITenantService tenantService,
        IUserService userService,
        ITenantContext tenantContext) : base(logger)
    {
        _emailGateway = emailGateway;
        _tenantService = tenantService;
        _userService = userService;
        _tenantContext = tenantContext;
    }

    public override string QueueName => QueueNames.EmailProcessing;

    public override async Task<OperationResult> HandleAsync(
        SendNotificationMessage message, 
        QueueMessage<SendNotificationMessage> queueMessage, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing email notification {MessageId} for recipient {Recipient}", 
                message.Id, message.Recipient);

            // Step 1: Validate tenant and user context
            var validationResult = await ValidateContextAsync(message, cancellationToken);
            if (!validationResult.IsSuccess)
            {
                return validationResult;
            }

            // Step 2: Save to database first (audit trail)
            var messageRecord = await SaveMessageToDatabase(message, queueMessage, cancellationToken);
            if (messageRecord == null)
            {
                return OperationResult.Failure("Failed to save message to database");
            }

            // Step 3: Send the email
            var sendResult = await SendEmailAsync(message, cancellationToken);
            
            // Step 4: Update database with result
            await UpdateMessageStatus(messageRecord, sendResult, cancellationToken);

            // Step 5: Send response/webhook if needed
            if (sendResult.IsSuccess)
            {
                await SendSuccessResponse(message, sendResult, cancellationToken);
                Logger.LogInformation("Successfully processed email notification {MessageId}", message.Id);
                return OperationResult.Success("Email sent successfully");
            }
            else
            {
                await SendFailureResponse(message, sendResult, cancellationToken);
                Logger.LogError("Failed to send email notification {MessageId}: {Error}", message.Id, sendResult.Message);
                return OperationResult.Failure($"Failed to send email: {sendResult.Message}");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Unexpected error processing email notification {MessageId}", message.Id);
            return OperationResult.Failure($"Unexpected error: {ex.Message}", ex);
        }
    }

    private async Task<OperationResult> ValidateContextAsync(SendNotificationMessage message, CancellationToken cancellationToken)
    {
        try
        {
            // Validate tenant
            if (!string.IsNullOrEmpty(message.TenantId))
            {
                var tenant = await _tenantService.GetTenantAsync(message.TenantId, cancellationToken);
                if (tenant == null)
                {
                    return OperationResult.Failure($"Tenant {message.TenantId} not found");
                }

                if (tenant.Status != TenantStatus.Active)
                {
                    return OperationResult.Failure($"Tenant {message.TenantId} is not active");
                }

                // Set tenant context
                _tenantContext.SetTenant(tenant.Id, tenant.Domain);
            }

            // Validate user if specified
            if (!string.IsNullOrEmpty(message.UserId))
            {
                var user = await _userService.GetUserAsync(message.UserId, cancellationToken);
                if (user == null)
                {
                    return OperationResult.Failure($"User {message.UserId} not found");
                }

                if (user.Status != UserStatus.Active)
                {
                    return OperationResult.Failure($"User {message.UserId} is not active");
                }
            }

            return OperationResult.Success("Context validation passed");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error validating context for message {MessageId}", message.Id);
            return OperationResult.Failure("Context validation failed", ex);
        }
    }

    private async Task<MessageRecord?> SaveMessageToDatabase(
        SendNotificationMessage message, 
        QueueMessage<SendNotificationMessage> queueMessage, 
        CancellationToken cancellationToken)
    {
        try
        {
            var messageRecord = new MessageRecord
            {
                Id = message.Id,
                TenantId = message.TenantId,
                UserId = message.UserId,
                Type = "Email",
                Recipient = message.Recipient,
                Subject = message.Subject,
                Content = message.Content,
                Status = MessageStatus.Processing,
                QueuedAt = queueMessage.EnqueuedAt,
                ProcessingStartedAt = DateTime.UtcNow,
                CorrelationId = message.CorrelationId,
                Metadata = message.Metadata,
                Headers = message.Headers
            };

            await SaveToDatabase(messageRecord, cancellationToken);
            Logger.LogDebug("Saved message {MessageId} to database", message.Id);
            return messageRecord;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to save message {MessageId} to database", message.Id);
            return null;
        }
    }

    private async Task<OperationResult> SendEmailAsync(SendNotificationMessage message, CancellationToken cancellationToken)
    {
        try
        {
            var emailRequest = new EmailMessageRequest
            {
                To = message.Recipient,
                From = message.From,
                Subject = message.Subject ?? "",
                Body = message.Content,
                HtmlBody = message.Metadata.TryGetValue("HtmlBody", out var htmlBody) ? htmlBody?.ToString() : null,
                PlainTextBody = message.Metadata.TryGetValue("PlainTextBody", out var plainBody) ? plainBody?.ToString() : null,
                Headers = message.Headers,
                Category = message.Metadata.TryGetValue("Category", out var category) ? category?.ToString() : null
            };

            // Add CC and BCC if specified
            if (message.Metadata.TryGetValue("Cc", out var cc) && !string.IsNullOrEmpty(cc?.ToString()))
            {
                emailRequest.Cc = new List<string> { cc.ToString()! };
            }

            if (message.Metadata.TryGetValue("Bcc", out var bcc) && !string.IsNullOrEmpty(bcc?.ToString()))
            {
                emailRequest.Bcc = new List<string> { bcc.ToString()! };
            }

            var result = await _emailGateway.SendAsync(emailRequest);
            
            if (result.IsSuccess)
            {
                return OperationResult.Success($"Email sent successfully. Message ID: {result.MessageId}");
            }
            else
            {
                return OperationResult.Failure(result.ErrorMessage ?? "Unknown error occurred");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error sending email for message {MessageId}", message.Id);
            return OperationResult.Failure("Failed to send email", ex);
        }
    }

    private async Task UpdateMessageStatus(MessageRecord messageRecord, OperationResult sendResult, CancellationToken cancellationToken)
    {
        try
        {
            messageRecord.Status = sendResult.IsSuccess ? MessageStatus.Sent : MessageStatus.Failed;
            messageRecord.ProcessingCompletedAt = DateTime.UtcNow;
            messageRecord.ErrorMessage = sendResult.IsSuccess ? null : sendResult.Message;

            await SaveToDatabase(messageRecord, cancellationToken);
            Logger.LogDebug("Updated message {MessageId} status to {Status}", messageRecord.Id, messageRecord.Status);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to update message {MessageId} status", messageRecord.Id);
        }
    }

    private async Task SendSuccessResponse(SendNotificationMessage message, OperationResult sendResult, CancellationToken cancellationToken)
    {
        try
        {
            // Send webhook or response if needed
            // This could trigger another queue message for webhook delivery
            Logger.LogDebug("Sending success response for message {MessageId}", message.Id);
            await Task.CompletedTask; // Placeholder for webhook/response logic
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to send success response for message {MessageId}", message.Id);
        }
    }

    private async Task SendFailureResponse(SendNotificationMessage message, OperationResult sendResult, CancellationToken cancellationToken)
    {
        try
        {
            // Send failure webhook or response if needed
            Logger.LogDebug("Sending failure response for message {MessageId}", message.Id);
            await Task.CompletedTask; // Placeholder for webhook/response logic
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to send failure response for message {MessageId}", message.Id);
        }
    }
}

/// <summary>
/// Database record for tracking messages
/// </summary>
public class MessageRecord
{
    public string Id { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Email, SMS, Push, etc.
    public string Recipient { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageStatus Status { get; set; }
    public DateTime QueuedAt { get; set; }
    public DateTime? ProcessingStartedAt { get; set; }
    public DateTime? ProcessingCompletedAt { get; set; }
    public string? CorrelationId { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public Dictionary<string, string> Headers { get; set; } = new();
}

/// <summary>
/// Message status enumeration
/// </summary>
public enum MessageStatus
{
    Queued = 0,
    Processing = 1,
    Sent = 2,
    Failed = 3,
    Cancelled = 4
}
