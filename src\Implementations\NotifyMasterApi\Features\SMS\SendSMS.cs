using FastEndpoints;
using NotifyMasterApi.Gateways;
using NotificationContract.Models;
using NotifyMasterApi.Documentation;
using System.ComponentModel.DataAnnotations;
using NotifyMaster.Core.Interfaces;
using static NotifyMaster.Core.Interfaces.Messages;

namespace NotifyMasterApi.Features.SMS;

/// <summary>
/// Request model for sending an SMS message
/// </summary>
public class SendSmsRequest
{
    /// <summary>
    /// Recipient phone number in international format (required)
    /// </summary>
    /// <example>+1234567890</example>
    [Required(ErrorMessage = "Phone number is required")]
    [Phone(ErrorMessage = "Invalid phone number format")]
    [RegularExpression(@"^\+[1-9]\d{1,14}$", ErrorMessage = "Phone number must be in international format (+1234567890)")]
    public string PhoneNumber { get; set; } = string.Empty;

    /// <summary>
    /// SMS message content (required)
    /// </summary>
    /// <example>Your verification code is: 123456</example>
    [Required(ErrorMessage = "Message content is required")]
    [StringLength(1600, MinimumLength = 1, ErrorMessage = "Message must be between 1 and 1600 characters")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Sender ID or phone number (optional - uses default if not specified)
    /// </summary>
    /// <example>YourService</example>
    [StringLength(15, ErrorMessage = "Sender ID cannot exceed 15 characters")]
    public string? From { get; set; }

    /// <summary>
    /// Additional metadata for tracking and analytics
    /// </summary>
    /// <example>{"campaign": "verification", "userId": "12345"}</example>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// Message type for provider-specific handling
    /// </summary>
    /// <example>transactional, promotional, otp</example>
    public string? MessageType { get; set; }

    /// <summary>
    /// Preferred SMS provider (optional - auto-selected if not specified)
    /// </summary>
    /// <example>Twilio, Kavenegar, Nexmo</example>
    public string? PreferredProvider { get; set; }
}

/// <summary>
/// Response model for SMS sending operation
/// </summary>
public class SendSmsResponse
{
    /// <summary>
    /// Indicates whether the SMS was queued successfully
    /// </summary>
    /// <example>true</example>
    public bool Success { get; set; }

    /// <summary>
    /// Correlation ID for tracking the request
    /// </summary>
    /// <example>corr_abc123def456</example>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Error message if the operation failed
    /// </summary>
    /// <example>Invalid phone number format</example>
    public string? Error { get; set; }

    /// <summary>
    /// Timestamp when the request was queued
    /// </summary>
    /// <example>2024-01-15T10:30:00Z</example>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Status of the request
    /// </summary>
    /// <example>Queued, Processing, Completed, Failed</example>
    public string Status { get; set; } = "Queued";

    /// <summary>
    /// Additional metadata about the operation
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

public class SendSmsEndpoint : Endpoint<SendSmsRequest, SendSmsResponse>
{
    private readonly IQueueService _queueService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<SendSmsEndpoint> _logger;

    public SendSmsEndpoint(IQueueService queueService, ITenantContext tenantContext, ILogger<SendSmsEndpoint> logger)
    {
        _queueService = queueService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public override void Configure()
    {
        this.ConfigureNotificationEndpoint(
            "POST",
            "/api/sms/send",
            "Send SMS Message",
            "Send an SMS message through the configured SMS providers with intelligent routing and delivery optimization.\n\n" +
            "## 🎯 Features\n" +
            "- **Multi-Provider Support**: Automatic failover between SMS providers\n" +
            "- **International Delivery**: Support for global SMS delivery\n" +
            "- **Smart Routing**: Optimal provider selection based on destination\n" +
            "- **Delivery Tracking**: Real-time delivery status updates\n" +
            "- **Cost Optimization**: Automatic cost-effective provider selection\n\n" +
            "## 📋 Provider Support\n" +
            "- Twilio (Global)\n- Kavenegar (Iran)\n- Nexmo/Vonage (Global)\n- Amazon SNS (Global)\n- Custom SMS Plugins\n\n" +
            "## 📱 Message Types\n" +
            "- **Transactional**: OTP, verification codes, alerts\n" +
            "- **Promotional**: Marketing messages, offers\n" +
            "- **Informational**: Updates, notifications\n\n" +
            "## ⚡ Rate Limits\n" +
            "- **Default**: 50 SMS/minute per API key\n" +
            "- **Burst**: Up to 200 SMS in 10 seconds\n" +
            "- **Daily**: 5,000 SMS per day (configurable)\n\n" +
            "## 🌍 International Support\n" +
            "- 200+ countries supported\n- Local number support where available\n" +
            "- Compliance with local regulations\n- Automatic character encoding (GSM 7-bit, UCS-2)",
            "SMS",
            new[] { "Core Messaging", "Mobile Communication" }
        );
    }

    public override async Task HandleAsync(SendSmsRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Queueing SMS to {PhoneNumber} for processing", req.PhoneNumber);

            // Create the message for the queue
            var smsMessage = new SendNotificationMessage
            {
                TenantId = _tenantContext.TenantId ?? "default",
                UserId = HttpContext.User?.Identity?.Name ?? "anonymous",
                PluginName = "sms",
                Recipient = req.PhoneNumber,
                Content = req.Message,
                From = req.From,
                Headers = new Dictionary<string, string>(),
                Metadata = new Dictionary<string, object>
                {
                    ["MessageType"] = req.MessageType ?? "transactional",
                    ["OriginalMetadata"] = req.Metadata ?? new Dictionary<string, object>()
                },
                RequestedAt = DateTime.UtcNow
            };

            // Enqueue the message
            var result = await _queueService.EnqueueAsync(QueueNames.SmsProcessing, smsMessage, ct);

            if (result.IsSuccess)
            {
                await SendOkAsync(new SendSmsResponse
                {
                    Success = true,
                    CorrelationId = smsMessage.CorrelationId,
                    Status = "Queued",
                    Metadata = new Dictionary<string, object>
                    {
                        ["QueueName"] = QueueNames.SmsProcessing,
                        ["JobId"] = result.Data ?? "unknown"
                    }
                }, ct);
            }
            else
            {
                await SendAsync(new SendSmsResponse
                {
                    Success = false,
                    Error = result.Message,
                    Status = "Failed"
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error queueing SMS");
            await SendAsync(new SendSmsResponse
            {
                Success = false,
                Error = "Internal server error",
                Status = "Failed"
            }, 500, ct);
        }
    }
}

public class SendBulkSmsRequest
{
    public List<SendSmsRequest> Messages { get; set; } = new();
}

public class SendBulkSmsResponse
{
    public bool Success { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<SendSmsResponse> Results { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class SendBulkSmsEndpoint : Endpoint<SendBulkSmsRequest, SendBulkSmsResponse>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<SendBulkSmsEndpoint> _logger;

    public SendBulkSmsEndpoint(ISmsGateway smsGateway, ILogger<SendBulkSmsEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/sms/send/bulk");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Send bulk SMS";
            s.Description = "Send multiple SMS messages through available SMS plugins";
            s.Responses[200] = "Bulk SMS processed";
            s.Responses[400] = "Invalid request";
            s.Responses[500] = "Internal server error";
        });
        Tags("SMS");
    }

    public override async Task HandleAsync(SendBulkSmsRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Sending bulk SMS to {Count} recipients", req.Messages.Count);

            var bulkRequest = new BulkSmsRequest
            {
                Messages = req.Messages.Select(m => new SmsMessageRequest
                {
                    PhoneNumber = m.PhoneNumber,
                    Message = m.Message,
                    From = m.From,
                    Metadata = m.Metadata
                }).ToList()
            };

            var result = await _smsGateway.SendBulkAsync(bulkRequest);

            var response = new SendBulkSmsResponse
            {
                Success = result.IsSuccess,
                SuccessCount = result.SuccessCount,
                FailureCount = result.FailureCount,
                Results = result.Results?.Select(r => new SendSmsResponse
                {
                    Success = r.IsSuccess,
                    MessageId = r.MessageId,
                    Error = r.ErrorMessage
                }).ToList() ?? new List<SendSmsResponse>()
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk SMS");
            await SendAsync(new SendBulkSmsResponse
            {
                Success = false,
                FailureCount = req.Messages.Count
            }, 500, ct);
        }
    }
}

public class GetSmsStatusRequest
{
    public string MessageId { get; set; } = string.Empty;
}

public class GetSmsStatusEndpoint : Endpoint<GetSmsStatusRequest, object>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<GetSmsStatusEndpoint> _logger;

    public GetSmsStatusEndpoint(ISmsGateway smsGateway, ILogger<GetSmsStatusEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/sms/status");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get SMS status";
            s.Description = "Get the status of an SMS message";
            s.Responses[200] = "SMS status retrieved successfully";
            s.Responses[404] = "Message not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("SMS");
    }

    public override async Task HandleAsync(GetSmsStatusRequest req, CancellationToken ct)
    {
        try
        {
            var status = await _smsGateway.GetMessageStatusAsync(req.MessageId);
            await SendOkAsync(status, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS status for {MessageId}", req.MessageId);
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetSmsProvidersEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<GetSmsProvidersEndpoint> _logger;

    public GetSmsProvidersEndpoint(ISmsGateway smsGateway, ILogger<GetSmsProvidersEndpoint> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/sms/providers");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get SMS providers";
            s.Description = "Get list of available SMS providers";
            s.Responses[200] = "Providers retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("SMS");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var providers = _smsGateway.GetAvailableProviders();
            await SendOkAsync(providers, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS providers");
            await SendErrorsAsync(500, ct);
        }
    }
}
