using FastEndpoints;
using NotifyMasterApi.Gateways;
using NotificationContract.Models;
using NotifyMasterApi.Documentation;
using System.ComponentModel.DataAnnotations;
using NotifyMaster.Core.Interfaces;
using static NotifyMaster.Core.Interfaces.Messages;

namespace NotifyMasterApi.Features.Email;

/// <summary>
/// Request model for sending an email message
/// </summary>
public class SendEmailRequest
{
    /// <summary>
    /// Recipient email address (required)
    /// </summary>
    /// <example><EMAIL></example>
    [Required(ErrorMessage = "Recipient email address is required")]
    [EmailAddress(ErrorMessage = "Invalid email address format")]
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Sender email address (optional - uses default if not specified)
    /// </summary>
    /// <example><EMAIL></example>
    [EmailAddress(ErrorMessage = "Invalid sender email address format")]
    public string From { get; set; } = string.Empty;

    /// <summary>
    /// Email subject line (required)
    /// </summary>
    /// <example>Welcome to NotificationService</example>
    [Required(ErrorMessage = "Email subject is required")]
    [StringLength(200, ErrorMessage = "Subject cannot exceed 200 characters")]
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// Email body content (deprecated - use HtmlBody or PlainTextBody)
    /// </summary>
    [Obsolete("Use HtmlBody or PlainTextBody instead")]
    public string? Body { get; set; }

    /// <summary>
    /// HTML formatted email body
    /// </summary>
    /// <example>&lt;h1&gt;Welcome!&lt;/h1&gt;&lt;p&gt;Thank you for joining us.&lt;/p&gt;</example>
    public string? HtmlBody { get; set; }

    /// <summary>
    /// Plain text email body (fallback for HTML)
    /// </summary>
    /// <example>Welcome! Thank you for joining us.</example>
    public string? PlainTextBody { get; set; }

    /// <summary>
    /// Carbon copy recipients (comma-separated)
    /// </summary>
    /// <example><EMAIL>,<EMAIL></example>
    public string? Cc { get; set; }

    /// <summary>
    /// Blind carbon copy recipients (comma-separated)
    /// </summary>
    /// <example><EMAIL>,<EMAIL></example>
    public string? Bcc { get; set; }

    /// <summary>
    /// Custom email headers
    /// </summary>
    /// <example>{"X-Priority": "1", "X-Custom-Header": "value"}</example>
    public Dictionary<string, string>? Headers { get; set; }

    /// <summary>
    /// Email category for tracking and analytics
    /// </summary>
    /// <example>welcome, notification, marketing</example>
    [StringLength(50, ErrorMessage = "Category cannot exceed 50 characters")]
    public string? Category { get; set; }
}

/// <summary>
/// Response model for email sending operation
/// </summary>
public class SendEmailResponse
{
    /// <summary>
    /// Indicates whether the email was queued successfully
    /// </summary>
    /// <example>true</example>
    public bool Success { get; set; }

    /// <summary>
    /// Correlation ID for tracking the request
    /// </summary>
    /// <example>corr_abc123def456</example>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Error message if the operation failed
    /// </summary>
    /// <example>Invalid email address format</example>
    public string? Error { get; set; }

    /// <summary>
    /// Timestamp when the request was queued
    /// </summary>
    /// <example>2024-01-15T10:30:00Z</example>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Status of the request
    /// </summary>
    /// <example>Queued, Processing, Completed, Failed</example>
    public string Status { get; set; } = "Queued";

    /// <summary>
    /// Additional metadata about the operation
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

public class SendEmailEndpoint : Endpoint<SendEmailRequest, SendEmailResponse>
{
    private readonly IQueueService _queueService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<SendEmailEndpoint> _logger;

    public SendEmailEndpoint(IQueueService queueService, ITenantContext tenantContext, ILogger<SendEmailEndpoint> logger)
    {
        _queueService = queueService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public override void Configure()
    {
        this.ConfigureNotificationEndpoint(
            "POST",
            "/api/email/send",
            "Send Email Message",
            "Send an email message through the configured email providers.\n\n" +
            "## 🎯 Features\n" +
            "- **Multi-Provider Support**: Automatically routes through available email providers\n" +
            "- **HTML & Plain Text**: Support for both HTML and plain text content\n" +
            "- **Advanced Recipients**: CC, BCC, and custom headers support\n" +
            "- **Tracking & Analytics**: Message categorization and delivery tracking\n" +
            "- **Validation**: Comprehensive input validation and error handling\n\n" +
            "## 📋 Provider Support\n" +
            "- SendGrid\n- Mailgun\n- Amazon SES\n- SMTP (Generic)\n- Custom Email Plugins\n\n" +
            "## ⚡ Rate Limits\n" +
            "- **Default**: 100 requests/minute per API key\n" +
            "- **Burst**: Up to 1000 requests in 10 seconds\n" +
            "- **Daily**: 10,000 emails per day (configurable)\n\n" +
            "## 🔒 Security\n" +
            "- Input sanitization for XSS prevention\n" +
            "- Email address validation\n" +
            "- Content filtering for spam prevention\n" +
            "- Rate limiting and abuse protection",
            "Email",
            new[] { "Core Messaging", "Communication" }
        );
    }

    public override async Task HandleAsync(SendEmailRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Queueing email to {To} for processing", req.To);

            // Create the message for the queue
            var emailMessage = new SendNotificationMessage
            {
                TenantId = _tenantContext.TenantId ?? "default",
                UserId = HttpContext.User?.Identity?.Name ?? "anonymous",
                PluginName = "email",
                Recipient = req.To,
                Content = req.PlainTextBody ?? req.HtmlBody ?? "",
                Subject = req.Subject,
                From = req.From,
                Headers = req.Headers ?? new Dictionary<string, string>(),
                Metadata = new Dictionary<string, object>
                {
                    ["HtmlBody"] = req.HtmlBody ?? "",
                    ["PlainTextBody"] = req.PlainTextBody ?? "",
                    ["Cc"] = req.Cc ?? "",
                    ["Bcc"] = req.Bcc ?? "",
                    ["Category"] = req.Category ?? ""
                },
                RequestedAt = DateTime.UtcNow
            };

            // Enqueue the message
            var result = await _queueService.EnqueueAsync(QueueNames.EmailProcessing, emailMessage, ct);

            if (result.IsSuccess)
            {
                await SendOkAsync(new SendEmailResponse
                {
                    Success = true,
                    CorrelationId = emailMessage.CorrelationId,
                    Status = "Queued",
                    Metadata = new Dictionary<string, object>
                    {
                        ["QueueName"] = QueueNames.EmailProcessing,
                        ["JobId"] = result.Data ?? "unknown"
                    }
                }, ct);
            }
            else
            {
                await SendAsync(new SendEmailResponse
                {
                    Success = false,
                    Error = result.Message,
                    Status = "Failed"
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error queueing email");
            await SendAsync(new SendEmailResponse
            {
                Success = false,
                Error = "Internal server error",
                Status = "Failed"
            }, 500, ct);
        }
    }
}

public class GetEmailProvidersEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IEmailGateway _emailGateway;
    private readonly ILogger<GetEmailProvidersEndpoint> _logger;

    public GetEmailProvidersEndpoint(IEmailGateway emailGateway, ILogger<GetEmailProvidersEndpoint> logger)
    {
        _emailGateway = emailGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/email/providers");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get email providers";
            s.Description = "Get list of available email providers";
            s.Responses[200] = "Providers retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var providers = await _emailGateway.GetProvidersAsync();
            await SendOkAsync(providers, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email providers");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class TestEmailProviderRequest
{
    public string Provider { get; set; } = string.Empty;
    public string? TestEmail { get; set; }
}

public class TestEmailProviderEndpoint : Endpoint<TestEmailProviderRequest, object>
{
    private readonly IEmailGateway _emailGateway;
    private readonly ILogger<TestEmailProviderEndpoint> _logger;

    public TestEmailProviderEndpoint(IEmailGateway emailGateway, ILogger<TestEmailProviderEndpoint> logger)
    {
        _emailGateway = emailGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/email/providers/{provider}/test");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Test email provider";
            s.Description = "Test a specific email provider";
            s.Responses[200] = "Provider test completed";
            s.Responses[500] = "Internal server error";
        });
        Tags("Email");
    }

    public override async Task HandleAsync(TestEmailProviderRequest req, CancellationToken ct)
    {
        try
        {
            var result = await _emailGateway.TestProviderAsync(req.Provider, req.TestEmail);
            await SendOkAsync(result, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing email provider {Provider}", req.Provider);
            await SendErrorsAsync(500, ct);
        }
    }
}
