{"sourceFile": "src/Core/NotifyMaster.Database/GlobalUsings.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751229755442, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751229755442, "name": "Commit-0", "content": "// Global using statements for NotifyMaster.Database project\n\n// System namespaces\nglobal using System;\nglobal using System.Collections.Generic;\nglobal using System.Linq;\nglobal using System.Threading;\nglobal using System.Threading.Tasks;\n\n// Microsoft Extensions\nglobal using Microsoft.Extensions.Logging;\n\n// Entity Framework\nglobal using Microsoft.EntityFrameworkCore;\n\n// NotifyMaster.Core namespaces\nglobal using NotifyMaster.Core.Interfaces;\nglobal using NotifyMaster.Core.Models;\n\n// NotifyMaster.Database namespaces\nglobal using NotifyMaster.Database;\n"}]}