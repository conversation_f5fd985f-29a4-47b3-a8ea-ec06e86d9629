{"sourceFile": "src/Implementations/NotifyMasterApi/NotifyMasterApi.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1751212535182, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751216933508, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,8 +13,9 @@\n     </PropertyGroup>\r\n \r\n     <ItemGroup>\r\n         <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.14\" />\r\n+        <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.1\" />\r\n         <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.1\" />\r\n         <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\r\n@@ -39,13 +40,12 @@\n \r\n     <ItemGroup>\r\n       <ProjectReference Include=\"..\\..\\Contracts\\NotificationContract\\NotificationContract.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Contracts\\PluginContract\\PluginContract.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Contracts\\EmailContract\\EmailContract.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Contracts\\SmsContract\\SmsContract.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Contracts\\PushNotificationContract\\PushNotificationContract.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Libraries\\EmailService.Library\\Email.Service.csproj\" />\r\n+      <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Libraries\\PushNotificationService.Library\\PushNotification.Service.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Libraries\\SmsService.Library\\Sms.Service.csproj\" />\r\n     </ItemGroup>\r\n \r\n"}, {"date": 1751224364527, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,39 +15,34 @@\n     <ItemGroup>\r\n         <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.14\" />\r\n         <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\r\n-        <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.1\" />\r\n-        <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.1\" />\r\n+        <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\r\n+        <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\r\n         <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\r\n         <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\r\n         <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\r\n         <PackageReference Include=\"Scalar.AspNetCore\" Version=\"1.2.42\" />\r\n         <PackageReference Include=\"FastEndpoints\" Version=\"5.30.0\" />\r\n         <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.14\" />\r\n         <PackageReference Include=\"Hangfire.SqlServer\" Version=\"1.8.14\" />\r\n-        <PackageReference Include=\"Hangfire.InMemory\" Version=\"0.10.4\" />\r\n-        <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.1.0\" />\r\n+        <PackageReference Include=\"Hangfire.InMemory\" Version=\"1.0.0\" />\r\n+        <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.2.0\" />\r\n         <PackageReference Include=\"System.Text.Json\" Version=\"9.0.0\" />\r\n-        <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.0\" />\r\n-        <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.0\" />\r\n-        <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.21.2\" />\r\n-        <PackageReference Include=\"AWSSDK.S3\" Version=\"3.7.402.8\" />\r\n-        <PackageReference Include=\"MediatR\" Version=\"12.4.1\" />\r\n+        <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.6\" />\r\n+        <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.6\" />\r\n+        <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.24.1\" />\r\n+        <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3.1\" />\r\n+        <PackageReference Include=\"MediatR\" Version=\"12.5.0\" />\r\n     </ItemGroup>\r\n \r\n \r\n \r\n     <ItemGroup>\r\n-      <ProjectReference Include=\"..\\..\\Contracts\\NotificationContract\\NotificationContract.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Contracts\\PluginContract\\PluginContract.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Contracts\\SmsContract\\SmsContract.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Contracts\\PushNotificationContract\\PushNotificationContract.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\r\n+      <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Libraries\\PushNotificationService.Library\\PushNotification.Service.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Libraries\\SmsService.Library\\Sms.Service.csproj\" />\r\n     </ItemGroup>\r\n \r\n     <ItemGroup>\r\n       <Folder Include=\"Presentation\" />\r\n"}, {"date": 1751226771587, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,9 +12,10 @@\n         <RootNamespace>NotifyMasterApi</RootNamespace>\r\n     </PropertyGroup>\r\n \r\n     <ItemGroup>\r\n-        <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.14\" />\r\n+        <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.20\" />\r\n+        <PackageReference Include=\"Hangfire.PostgreSql\" Version=\"1.20.12\" />\r\n         <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\r\n"}], "date": 1751212535181, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk.Web\">\r\n\r\n    <PropertyGroup>\r\n        <TargetFramework>net9.0</TargetFramework>\r\n        <LangVersion>latest</LangVersion>\r\n        <Nullable>enable</Nullable>\r\n        <ImplicitUsings>enable</ImplicitUsings>\r\n        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>\r\n        <GenerateDocumentationFile>true</GenerateDocumentationFile>\r\n        <NoWarn>$(NoWarn);1591</NoWarn>\r\n        <AssemblyName>NotifyMasterApi</AssemblyName>\r\n        <RootNamespace>NotifyMasterApi</RootNamespace>\r\n    </PropertyGroup>\r\n\r\n    <ItemGroup>\r\n        <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.14\" />\r\n        <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\r\n        <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.1\" />\r\n        <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.1\" />\r\n        <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\r\n        <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\r\n        <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\r\n        <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\r\n        <PackageReference Include=\"Scalar.AspNetCore\" Version=\"1.2.42\" />\r\n        <PackageReference Include=\"FastEndpoints\" Version=\"5.30.0\" />\r\n        <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.14\" />\r\n        <PackageReference Include=\"Hangfire.SqlServer\" Version=\"1.8.14\" />\r\n        <PackageReference Include=\"Hangfire.InMemory\" Version=\"0.10.4\" />\r\n        <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.1.0\" />\r\n        <PackageReference Include=\"System.Text.Json\" Version=\"9.0.0\" />\r\n        <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.0\" />\r\n        <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.0\" />\r\n        <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.21.2\" />\r\n        <PackageReference Include=\"AWSSDK.S3\" Version=\"3.7.402.8\" />\r\n        <PackageReference Include=\"MediatR\" Version=\"12.4.1\" />\r\n    </ItemGroup>\r\n\r\n\r\n\r\n    <ItemGroup>\r\n      <ProjectReference Include=\"..\\..\\Contracts\\NotificationContract\\NotificationContract.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Contracts\\PluginContract\\PluginContract.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Contracts\\EmailContract\\EmailContract.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Contracts\\SmsContract\\SmsContract.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Contracts\\PushNotificationContract\\PushNotificationContract.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Libraries\\EmailService.Library\\Email.Service.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Libraries\\PushNotificationService.Library\\PushNotification.Service.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Libraries\\SmsService.Library\\Sms.Service.csproj\" />\r\n    </ItemGroup>\r\n\r\n    <ItemGroup>\r\n      <Folder Include=\"Presentation\" />\r\n    </ItemGroup>\r\n\r\n</Project>\r\n"}]}