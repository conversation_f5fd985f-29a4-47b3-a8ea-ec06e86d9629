{"sourceFile": "src/Core/NotifyMaster.Core/Extensions/ServiceCollectionExtensions.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1751225331300, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751229631911, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,13 +13,62 @@\n     public static IServiceCollection AddCoreServices(this IServiceCollection services)\n     {\n         services.AddScoped<ITenantResolutionService, TenantResolutionService>();\n         services.AddScoped<IAuthorizationService, AuthorizationService>();\n-        \n+\n         return services;\n     }\n \n     /// <summary>\n+    /// Adds authentication services to the service collection\n+    /// </summary>\n+    public static IServiceCollection AddAuthenticationServices(this IServiceCollection services, IConfiguration configuration)\n+    {\n+        // Configure JWT options\n+        services.Configure<JwtOptions>(options => configuration.GetSection(\"Jwt\").Bind(options));\n+\n+        // Add authentication service\n+        services.AddScoped<IAuthenticationService, AuthenticationService>();\n+\n+        return services;\n+    }\n+\n+    /// <summary>\n+    /// Adds tenant context services\n+    /// </summary>\n+    public static IServiceCollection AddTenantContext(this IServiceCollection services)\n+    {\n+        services.AddScoped<ITenantContext, TenantContext>();\n+        return services;\n+    }\n+\n+    /// <summary>\n+    /// Adds authorization policies\n+    /// </summary>\n+    public static IServiceCollection AddAuthorizationPolicies(this IServiceCollection services)\n+    {\n+        services.AddAuthorization(options =>\n+        {\n+            // Add tenant-based policies\n+            options.AddPolicy(\"TenantAccess\", policy =>\n+                policy.RequireAuthenticatedUser()\n+                      .RequireClaim(\"tenant_id\"));\n+\n+            // Add role-based policies\n+            options.AddPolicy(\"AdminOnly\", policy =>\n+                policy.RequireRole(\"Administrator\"));\n+\n+            options.AddPolicy(\"UserManagement\", policy =>\n+                policy.RequireClaim(\"permission\", \"users:manage\"));\n+\n+            options.AddPolicy(\"TenantManagement\", policy =>\n+                policy.RequireClaim(\"permission\", \"tenants:manage\"));\n+        });\n+\n+        return services;\n+    }\n+\n+    /// <summary>\n     /// Adds JWT authentication with multitenancy support\n     /// </summary>\n     public static IServiceCollection AddJwtAuthentication(this IServiceCollection services, IConfiguration configuration)\n     {\n"}, {"date": 1751229866536, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -41,8 +41,17 @@\n         return services;\n     }\n \n     /// <summary>\n+    /// Adds tenant-aware plugin manager\n+    /// </summary>\n+    public static IServiceCollection AddTenantAwarePluginManager(this IServiceCollection services)\n+    {\n+        services.AddScoped<ITenantAwarePluginManager, TenantAwarePluginManager>();\n+        return services;\n+    }\n+\n+    /// <summary>\n     /// Adds authorization policies\n     /// </summary>\n     public static IServiceCollection AddAuthorizationPolicies(this IServiceCollection services)\n     {\n"}, {"date": 1751230001996, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -136,71 +136,9 @@\n \n         return services;\n     }\n \n-    /// <summary>\n-    /// Adds authorization policies for multitenancy\n-    /// </summary>\n-    public static IServiceCollection AddAuthorizationPolicies(this IServiceCollection services)\n-    {\n-        services.AddAuthorizationBuilder()\n-            .AddPolicy(\"RequireAuthentication\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-            })\n-            .AddPolicy(\"RequireSystemAdmin\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-                policy.RequireRole(\"SuperAdmin\");\n-            })\n-            .AddPolicy(\"RequireTenantAdmin\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-                policy.RequireRole(\"TenantAdmin\", \"SuperAdmin\");\n-            })\n-            .AddPolicy(\"RequireUser\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-                policy.RequireRole(\"User\", \"TenantAdmin\", \"SuperAdmin\");\n-            })\n-            .AddPolicy(\"RequirePluginManagement\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-                policy.RequireClaim(\"permission\", \"plugins:manage\");\n-            })\n-            .AddPolicy(\"RequireUserManagement\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-                policy.RequireClaim(\"permission\", \"users:manage\");\n-            })\n-            .AddPolicy(\"RequireMessageSend\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-                policy.RequireClaim(\"permission\", \"messages:send\");\n-            })\n-            .AddPolicy(\"RequireTemplateManagement\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-                policy.RequireClaim(\"permission\", \"templates:manage\");\n-            })\n-            .AddPolicy(\"RequireWebhookManagement\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-                policy.RequireClaim(\"permission\", \"webhooks:manage\");\n-            })\n-            .AddPolicy(\"RequireSystemSettings\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-                policy.RequireClaim(\"permission\", \"system:settings\");\n-            })\n-            .AddPolicy(\"RequireReports\", policy =>\n-            {\n-                policy.RequireAuthenticatedUser();\n-                policy.RequireClaim(\"permission\", \"reports:view\");\n-            });\n \n-        return services;\n-    }\n \n     /// <summary>\n     /// Adds CORS policies for multitenancy\n     /// </summary>\n"}, {"date": 1751231189234, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -136,10 +136,27 @@\n \n         return services;\n     }\n \n+    /// <summary>\n+    /// Adds queue services for message processing\n+    /// </summary>\n+    public static IServiceCollection AddQueueServices(this IServiceCollection services)\n+    {\n+        // Add queue service implementation\n+        services.AddScoped<IQueueService, HangfireQueueService>();\n \n+        // Add queue processor\n+        services.AddScoped<IQueueProcessor, QueueProcessor>();\n \n+        // Add message handlers\n+        services.AddScoped<IMessageHandler<SendNotificationMessage>, SendNotificationMessageHandler>();\n+\n+        return services;\n+    }\n+\n+\n+\n     /// <summary>\n     /// Adds CORS policies for multitenancy\n     /// </summary>\n     public static IServiceCollection AddCorsPolicies(this IServiceCollection services)\n"}, {"date": 1751231245970, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -147,10 +147,10 @@\n \n         // Add queue processor\n         services.AddScoped<IQueueProcessor, QueueProcessor>();\n \n-        // Add message handlers\n-        services.AddScoped<IMessageHandler<SendNotificationMessage>, SendNotificationMessageHandler>();\n+        // Add message handlers - these will be registered by the API project\n+        // services.AddScoped<IMessageHandler<SendNotificationMessage>, SendNotificationMessageHandler>();\n \n         return services;\n     }\n \n"}], "date": 1751225331300, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Core.Extensions;\n\n/// <summary>\n/// Extension methods for IServiceCollection to register core services\n/// </summary>\npublic static class ServiceCollectionExtensions\n{\n    /// <summary>\n    /// Adds core multitenancy services\n    /// </summary>\n    public static IServiceCollection AddCoreServices(this IServiceCollection services)\n    {\n        services.AddScoped<ITenantResolutionService, TenantResolutionService>();\n        services.AddScoped<IAuthorizationService, AuthorizationService>();\n        \n        return services;\n    }\n\n    /// <summary>\n    /// Adds JWT authentication with multitenancy support\n    /// </summary>\n    public static IServiceCollection AddJwtAuthentication(this IServiceCollection services, IConfiguration configuration)\n    {\n        var jwtSettings = configuration.GetSection(\"Jwt\");\n        var secretKey = jwtSettings[\"SecretKey\"] ?? \"your-super-secret-key-that-is-at-least-32-characters-long\";\n        var issuer = jwtSettings[\"Issuer\"] ?? \"NotifyMaster\";\n        var audience = jwtSettings[\"Audience\"] ?? \"NotifyMaster\";\n\n        services.AddAuthentication(options =>\n        {\n            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;\n            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;\n        })\n        .AddJwtBearer(options =>\n        {\n            options.TokenValidationParameters = new TokenValidationParameters\n            {\n                ValidateIssuer = true,\n                ValidateAudience = true,\n                ValidateLifetime = true,\n                ValidateIssuerSigningKey = true,\n                ValidIssuer = issuer,\n                ValidAudience = audience,\n                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),\n                ClockSkew = TimeSpan.FromMinutes(5)\n            };\n\n            options.Events = new JwtBearerEvents\n            {\n                OnTokenValidated = async context =>\n                {\n                    // Additional validation can be added here\n                    // For example, checking if the user still exists and is active\n                    var userIdClaim = context.Principal?.FindFirst(\"sub\")?.Value;\n                    var tenantIdClaim = context.Principal?.FindFirst(\"tenant_id\")?.Value;\n\n                    if (string.IsNullOrEmpty(userIdClaim) || string.IsNullOrEmpty(tenantIdClaim))\n                    {\n                        context.Fail(\"Invalid token claims\");\n                        return;\n                    }\n\n                    // You can add additional validation here\n                    // For example, check if user is still active in the database\n                },\n                OnAuthenticationFailed = context =>\n                {\n                    if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))\n                    {\n                        context.Response.Headers.Add(\"Token-Expired\", \"true\");\n                    }\n                    return Task.CompletedTask;\n                }\n            };\n        });\n\n        return services;\n    }\n\n    /// <summary>\n    /// Adds authorization policies for multitenancy\n    /// </summary>\n    public static IServiceCollection AddAuthorizationPolicies(this IServiceCollection services)\n    {\n        services.AddAuthorizationBuilder()\n            .AddPolicy(\"RequireAuthentication\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n            })\n            .AddPolicy(\"RequireSystemAdmin\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n                policy.RequireRole(\"SuperAdmin\");\n            })\n            .AddPolicy(\"RequireTenantAdmin\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n                policy.RequireRole(\"TenantAdmin\", \"SuperAdmin\");\n            })\n            .AddPolicy(\"RequireUser\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n                policy.RequireRole(\"User\", \"TenantAdmin\", \"SuperAdmin\");\n            })\n            .AddPolicy(\"RequirePluginManagement\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n                policy.RequireClaim(\"permission\", \"plugins:manage\");\n            })\n            .AddPolicy(\"RequireUserManagement\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n                policy.RequireClaim(\"permission\", \"users:manage\");\n            })\n            .AddPolicy(\"RequireMessageSend\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n                policy.RequireClaim(\"permission\", \"messages:send\");\n            })\n            .AddPolicy(\"RequireTemplateManagement\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n                policy.RequireClaim(\"permission\", \"templates:manage\");\n            })\n            .AddPolicy(\"RequireWebhookManagement\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n                policy.RequireClaim(\"permission\", \"webhooks:manage\");\n            })\n            .AddPolicy(\"RequireSystemSettings\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n                policy.RequireClaim(\"permission\", \"system:settings\");\n            })\n            .AddPolicy(\"RequireReports\", policy =>\n            {\n                policy.RequireAuthenticatedUser();\n                policy.RequireClaim(\"permission\", \"reports:view\");\n            });\n\n        return services;\n    }\n\n    /// <summary>\n    /// Adds CORS policies for multitenancy\n    /// </summary>\n    public static IServiceCollection AddCorsPolicies(this IServiceCollection services)\n    {\n        services.AddCors(options =>\n        {\n            options.AddPolicy(\"DefaultPolicy\", builder =>\n            {\n                builder\n                    .AllowAnyOrigin()\n                    .AllowAnyMethod()\n                    .AllowAnyHeader();\n            });\n\n            options.AddPolicy(\"RestrictivePolicy\", builder =>\n            {\n                builder\n                    .WithOrigins(\"https://localhost:5001\", \"https://localhost:7001\")\n                    .WithMethods(\"GET\", \"POST\", \"PUT\", \"DELETE\")\n                    .WithHeaders(\"Content-Type\", \"Authorization\", \"X-Tenant-Id\", \"X-Tenant-Domain\")\n                    .AllowCredentials();\n            });\n        });\n\n        return services;\n    }\n}\n"}]}