// Using statements are handled by GlobalUsings.cs

namespace PluginCore.Services;

/// <summary>
/// JWT configuration options
/// </summary>
public class JwtOptions
{
    public string SecretKey { get; set; } = string.Empty;
    public string Issuer { get; set; } = "NotifyMaster";
    public string Audience { get; set; } = "NotifyMaster";
    public int ExpirationMinutes { get; set; } = 60;
    public int RefreshTokenExpirationDays { get; set; } = 7;
}

/// <summary>
/// Interface for authentication service
/// </summary>
public interface IAuthenticationService
{
    /// <summary>
    /// Authenticates a user and returns JWT token
    /// </summary>
    Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Refreshes an authentication token
    /// </summary>
    Task<OperationResult<AuthenticationResult>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Validates a JWT token
    /// </summary>
    Task<OperationResult<ClaimsPrincipal>> ValidateTokenAsync(string token, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Revokes a refresh token
    /// </summary>
    Task<OperationResult> RevokeTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Generates a JWT token for a user
    /// </summary>
    Task<string> GenerateTokenAsync(User user, IReadOnlyList<Role> roles, IReadOnlyList<Permission> permissions);
    
    /// <summary>
    /// Generates a refresh token
    /// </summary>
    string GenerateRefreshToken();
    
    /// <summary>
    /// Hashes a password
    /// </summary>
    string HashPassword(string password);
    
    /// <summary>
    /// Verifies a password against a hash
    /// </summary>
    bool VerifyPassword(string password, string hash);
}

/// <summary>
/// Implementation of authentication service
/// </summary>
public class AuthenticationService : IAuthenticationService
{
    private readonly ILogger<AuthenticationService> _logger;
    private readonly IUserService _userService;
    private readonly IRoleService _roleService;
    private readonly IOptions<JwtOptions> _jwtOptions;
    private readonly TokenValidationParameters _tokenValidationParameters;

    public AuthenticationService(
        ILogger<AuthenticationService> logger,
        IUserService userService,
        IRoleService roleService,
        IOptions<JwtOptions> jwtOptions)
    {
        _logger = logger;
        _userService = userService;
        _roleService = roleService;
        _jwtOptions = jwtOptions;
        
        _tokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtOptions.Value.SecretKey)),
            ValidateIssuer = true,
            ValidIssuer = _jwtOptions.Value.Issuer,
            ValidateAudience = true,
            ValidAudience = _jwtOptions.Value.Audience,
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };
    }

    public async Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get user by email within tenant
            var user = await _userService.GetUserByEmailAsync(tenantId, email, cancellationToken);
            if (user == null)
            {
                _logger.LogWarning("Authentication failed: User not found for email {Email} in tenant {TenantId}", email, tenantId);
                return OperationResult<AuthenticationResult>.Failure("Invalid email or password");
            }

            // Check if user is active
            if (user.Status != UserStatus.Active)
            {
                _logger.LogWarning("Authentication failed: User {UserId} is not active (Status: {Status})", user.Id, user.Status);
                return OperationResult<AuthenticationResult>.Failure("User account is not active");
            }

            // Verify password
            if (!VerifyPassword(password, user.PasswordHash))
            {
                _logger.LogWarning("Authentication failed: Invalid password for user {UserId}", user.Id);
                return OperationResult<AuthenticationResult>.Failure("Invalid email or password");
            }

            // Get user roles and permissions
            var roles = await _userService.GetUserRolesAsync(user.Id, tenantId, cancellationToken);
            var permissions = await _userService.GetUserPermissionsAsync(user.Id, tenantId, cancellationToken);

            // Generate tokens
            var token = await GenerateTokenAsync(user, roles, permissions);
            var refreshToken = GenerateRefreshToken();

            // Update last login time
            await _userService.UpdateUserAsync(user.Id, new UpdateUserRequest(), cancellationToken);

            var result = new AuthenticationResult
            {
                User = user,
                Token = token,
                ExpiresAt = DateTime.UtcNow.AddMinutes(_jwtOptions.Value.ExpirationMinutes),
                RefreshToken = refreshToken,
                Roles = roles,
                Permissions = permissions
            };

            _logger.LogInformation("User {UserId} authenticated successfully", user.Id);
            return OperationResult<AuthenticationResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during authentication for email {Email} in tenant {TenantId}", email, tenantId);
            return OperationResult<AuthenticationResult>.Failure("Authentication failed");
        }
    }

    public async Task<OperationResult<AuthenticationResult>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        try
        {
            // In a real implementation, you would validate the refresh token against a database
            // For now, we'll just return a failure
            _logger.LogWarning("Refresh token validation not implemented");
            return OperationResult<AuthenticationResult>.Failure("Refresh token is invalid or expired");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return OperationResult<AuthenticationResult>.Failure("Token refresh failed");
        }
    }

    public async Task<OperationResult<ClaimsPrincipal>> ValidateTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var principal = tokenHandler.ValidateToken(token, _tokenValidationParameters, out var validatedToken);
            
            return OperationResult<ClaimsPrincipal>.Success(principal);
        }
        catch (SecurityTokenException ex)
        {
            _logger.LogWarning(ex, "Token validation failed");
            return OperationResult<ClaimsPrincipal>.Failure("Invalid token");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token validation");
            return OperationResult<ClaimsPrincipal>.Failure("Token validation failed");
        }
    }

    public async Task<OperationResult> RevokeTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        try
        {
            // In a real implementation, you would mark the refresh token as revoked in the database
            _logger.LogInformation("Refresh token revoked");
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token revocation");
            return OperationResult.Failure("Token revocation failed");
        }
    }

    public async Task<string> GenerateTokenAsync(User user, IReadOnlyList<Role> roles, IReadOnlyList<Permission> permissions)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id),
            new(ClaimTypes.Name, user.Username),
            new(ClaimTypes.Email, user.Email),
            new("tenant_id", user.TenantId),
            new("user_id", user.Id),
            new("first_name", user.FirstName ?? string.Empty),
            new("last_name", user.LastName ?? string.Empty)
        };

        // Add role claims
        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role.Name));
        }

        // Add permission claims
        foreach (var permission in permissions)
        {
            claims.Add(new Claim("permission", $"{permission.Resource}:{permission.Action}"));
        }

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtOptions.Value.SecretKey));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: _jwtOptions.Value.Issuer,
            audience: _jwtOptions.Value.Audience,
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(_jwtOptions.Value.ExpirationMinutes),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public string GenerateRefreshToken()
    {
        var randomBytes = new byte[64];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        return Convert.ToBase64String(randomBytes);
    }

    public string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt());
    }

    public bool VerifyPassword(string password, string hash)
    {
        try
        {
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Extension methods for authentication services
/// </summary>
public static class AuthenticationServiceExtensions
{
    /// <summary>
    /// Adds authentication services to the service collection
    /// </summary>
    public static IServiceCollection AddAuthenticationServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure JWT options
        services.Configure<JwtOptions>(options => configuration.GetSection("Jwt").Bind(options));
        
        // Add authentication service
        services.AddScoped<IAuthenticationService, AuthenticationService>();
        
        return services;
    }
}
