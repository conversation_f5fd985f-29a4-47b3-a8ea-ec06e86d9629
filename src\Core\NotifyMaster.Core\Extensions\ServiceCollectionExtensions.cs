// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Extensions;

/// <summary>
/// Extension methods for IServiceCollection to register core services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds core multitenancy services
    /// </summary>
    public static IServiceCollection AddCoreServices(this IServiceCollection services)
    {
        services.AddScoped<ITenantResolutionService, TenantResolutionService>();
        services.AddScoped<IAuthorizationService, AuthorizationService>();

        return services;
    }

    /// <summary>
    /// Adds authentication services to the service collection
    /// </summary>
    public static IServiceCollection AddAuthenticationServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure JWT options
        services.Configure<JwtOptions>(options => configuration.GetSection("Jwt").Bind(options));

        // Add authentication service
        services.AddScoped<IAuthenticationService, AuthenticationService>();

        return services;
    }

    /// <summary>
    /// Adds tenant context services
    /// </summary>
    public static IServiceCollection AddTenantContext(this IServiceCollection services)
    {
        services.AddScoped<ITenantContext, TenantContext>();
        return services;
    }

    /// <summary>
    /// Adds tenant-aware plugin manager
    /// </summary>
    public static IServiceCollection AddTenantAwarePluginManager(this IServiceCollection services)
    {
        services.AddScoped<ITenantAwarePluginManager, TenantAwarePluginManager>();
        return services;
    }

    /// <summary>
    /// Adds authorization policies
    /// </summary>
    public static IServiceCollection AddAuthorizationPolicies(this IServiceCollection services)
    {
        services.AddAuthorization(options =>
        {
            // Add tenant-based policies
            options.AddPolicy("TenantAccess", policy =>
                policy.RequireAuthenticatedUser()
                      .RequireClaim("tenant_id"));

            // Add role-based policies
            options.AddPolicy("AdminOnly", policy =>
                policy.RequireRole("Administrator"));

            options.AddPolicy("UserManagement", policy =>
                policy.RequireClaim("permission", "users:manage"));

            options.AddPolicy("TenantManagement", policy =>
                policy.RequireClaim("permission", "tenants:manage"));
        });

        return services;
    }

    /// <summary>
    /// Adds JWT authentication with multitenancy support
    /// </summary>
    public static IServiceCollection AddJwtAuthentication(this IServiceCollection services, IConfiguration configuration)
    {
        var jwtSettings = configuration.GetSection("Jwt");
        var secretKey = jwtSettings["SecretKey"] ?? "your-super-secret-key-that-is-at-least-32-characters-long";
        var issuer = jwtSettings["Issuer"] ?? "NotifyMaster";
        var audience = jwtSettings["Audience"] ?? "NotifyMaster";

        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = issuer,
                ValidAudience = audience,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            options.Events = new JwtBearerEvents
            {
                OnTokenValidated = async context =>
                {
                    // Additional validation can be added here
                    // For example, checking if the user still exists and is active
                    var userIdClaim = context.Principal?.FindFirst("sub")?.Value;
                    var tenantIdClaim = context.Principal?.FindFirst("tenant_id")?.Value;

                    if (string.IsNullOrEmpty(userIdClaim) || string.IsNullOrEmpty(tenantIdClaim))
                    {
                        context.Fail("Invalid token claims");
                        return;
                    }

                    // You can add additional validation here
                    // For example, check if user is still active in the database
                },
                OnAuthenticationFailed = context =>
                {
                    if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
                    {
                        context.Response.Headers.Add("Token-Expired", "true");
                    }
                    return Task.CompletedTask;
                }
            };
        });

        return services;
    }

    /// <summary>
    /// Adds queue services for message processing
    /// </summary>
    public static IServiceCollection AddQueueServices(this IServiceCollection services)
    {
        // Add queue service implementation
        services.AddScoped<IQueueService, HangfireQueueService>();

        // Add queue processor
        services.AddScoped<IQueueProcessor, QueueProcessor>();

        // Add message handlers - these will be registered by the API project
        // services.AddScoped<IMessageHandler<SendNotificationMessage>, SendNotificationMessageHandler>();

        return services;
    }



    /// <summary>
    /// Adds CORS policies for multitenancy
    /// </summary>
    public static IServiceCollection AddCorsPolicies(this IServiceCollection services)
    {
        services.AddCors(options =>
        {
            options.AddPolicy("DefaultPolicy", builder =>
            {
                builder
                    .AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader();
            });

            options.AddPolicy("RestrictivePolicy", builder =>
            {
                builder
                    .WithOrigins("https://localhost:5001", "https://localhost:7001")
                    .WithMethods("GET", "POST", "PUT", "DELETE")
                    .WithHeaders("Content-Type", "Authorization", "X-Tenant-Id", "X-Tenant-Domain")
                    .AllowCredentials();
            });
        });

        return services;
    }
}
