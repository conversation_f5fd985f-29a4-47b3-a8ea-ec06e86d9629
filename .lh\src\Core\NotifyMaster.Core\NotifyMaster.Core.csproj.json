{"sourceFile": "src/Core/NotifyMaster.Core/NotifyMaster.Core.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751224627211, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751228525432, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,9 +9,9 @@\n   <ItemGroup>\n     <PackageReference Include=\"Microsoft.Extensions.DependencyInjection.Abstractions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Configuration.Abstractions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Logging.Abstractions\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.2.0\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.3.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.12.1\" />\n   </ItemGroup>\n \n"}], "date": 1751224627211, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk\">\n\n  <PropertyGroup>\n    <TargetFramework>net9.0</TargetFramework>\n    <ImplicitUsings>enable</ImplicitUsings>\n    <Nullable>enable</Nullable>\n  </PropertyGroup>\n\n  <ItemGroup>\n    <PackageReference Include=\"Microsoft.Extensions.DependencyInjection.Abstractions\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.Extensions.Configuration.Abstractions\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.Extensions.Logging.Abstractions\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.2.0\" />\n    <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n    <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.12.1\" />\n  </ItemGroup>\n\n  <ItemGroup>\n    <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n  </ItemGroup>\n\n</Project>\n"}]}