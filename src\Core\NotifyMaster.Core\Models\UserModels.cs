// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Models;

/// <summary>
/// Represents a user in the system
/// </summary>
public class User
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string TenantId { get; set; } = string.Empty;
    
    [Required, MaxLength(100)]
    public string Username { get; set; } = string.Empty;
    
    [Required, Max<PERSON>ength(255), EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? FirstName { get; set; }
    
    [MaxLength(100)]
    public string? LastName { get; set; }
    
    [Required]
    public string PasswordHash { get; set; } = string.Empty;
    
    public UserStatus Status { get; set; } = UserStatus.Active;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public DateTime? LastLoginAt { get; set; }
    
    public string? CreatedBy { get; set; }
    
    public string? UpdatedBy { get; set; }
    
    public Dictionary<string, object> Profile { get; set; } = new();
    
    // Navigation properties
    public Tenant Tenant { get; set; } = null!;
    public List<UserRole> Roles { get; set; } = new();
    public List<UserPermission> Permissions { get; set; } = new();
}

/// <summary>
/// User status enumeration
/// </summary>
public enum UserStatus
{
    Active = 0,
    Inactive = 1,
    Suspended = 2,
    PendingActivation = 3,
    Deleted = 4
}

/// <summary>
/// Represents a role in the system
/// </summary>
public class Role
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required, MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public string? TenantId { get; set; }
    
    public RoleScope Scope { get; set; } = RoleScope.Tenant;
    
    public bool IsSystemRole { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? CreatedBy { get; set; }
    
    public string? UpdatedBy { get; set; }
    
    // Navigation properties
    public List<UserRole> Users { get; set; } = new();
    public List<RolePermission> Permissions { get; set; } = new();
}

/// <summary>
/// Role scope enumeration
/// </summary>
public enum RoleScope
{
    System = 0,
    Tenant = 1
}

/// <summary>
/// Represents a permission in the system
/// </summary>
public class Permission
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required, MaxLength(100)]
    public string Resource { get; set; } = string.Empty;
    
    [Required, MaxLength(100)]
    public string Action { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public bool IsSystemPermission { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public string? CreatedBy { get; set; }
    
    // Navigation properties
    public List<UserPermission> Users { get; set; } = new();
    public List<RolePermission> Roles { get; set; } = new();
}

/// <summary>
/// Many-to-many relationship between users and roles
/// </summary>
public class UserRole
{
    public string UserId { get; set; } = string.Empty;
    public string RoleId { get; set; } = string.Empty;
    public string? TenantId { get; set; }
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    public string? AssignedBy { get; set; }
    
    // Navigation properties
    public User User { get; set; } = null!;
    public Role Role { get; set; } = null!;
}

/// <summary>
/// Many-to-many relationship between users and permissions
/// </summary>
public class UserPermission
{
    public string UserId { get; set; } = string.Empty;
    public string PermissionId { get; set; } = string.Empty;
    public string? TenantId { get; set; }
    public DateTime GrantedAt { get; set; } = DateTime.UtcNow;
    public string? GrantedBy { get; set; }
    
    // Navigation properties
    public User User { get; set; } = null!;
    public Permission Permission { get; set; } = null!;
}

/// <summary>
/// Many-to-many relationship between roles and permissions
/// </summary>
public class RolePermission
{
    public string RoleId { get; set; } = string.Empty;
    public string PermissionId { get; set; } = string.Empty;
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    public string? AssignedBy { get; set; }
    
    // Navigation properties
    public Role Role { get; set; } = null!;
    public Permission Permission { get; set; } = null!;
}
